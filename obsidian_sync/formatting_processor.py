"""
Formatting Processor
Specialized processors for bold text, italic, code, and other formatting with fallback mechanisms
"""

import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from .utils import logger, grapheme_len


@dataclass
class FormattingRange:
    """Represents a formatting range in text"""
    start: int
    end: int
    format_type: str
    properties: Dict = None


class FormattingProcessor:
    """Enhanced formatting processor with fallback mechanisms"""
    
    def __init__(self):
        """Initialize formatting processor"""
        self.formatting_patterns = {
            'bold': [
                r'\*\*([^*]+)\*\*',  # **bold**
                r'__([^_]+)__'       # __bold__
            ],
            'italic': [
                r'\*([^*]+)\*',      # *italic*
                r'_([^_]+)_'         # _italic_
            ],
            'code': [
                r'`([^`]+)`'         # `code`
            ],
            'strikethrough': [
                r'~~([^~]+)~~'       # ~~strikethrough~~
            ]
        }
    
    def extract_formatting(self, text: str) -> Tuple[str, List[FormattingRange]]:
        """
        Extract formatting from text and return clean text with formatting ranges
        
        Args:
            text: Text with markdown formatting
            
        Returns:
            Tuple of (clean_text, formatting_ranges)
        """
        formatting_ranges = []
        clean_text = text
        offset = 0
        
        # Process each formatting type
        for format_type, patterns in self.formatting_patterns.items():
            for pattern in patterns:
                clean_text, ranges, offset = self._extract_format_type(
                    clean_text, pattern, format_type, offset
                )
                formatting_ranges.extend(ranges)
        
        # Sort ranges by start position
        formatting_ranges.sort(key=lambda r: r.start)
        
        return clean_text, formatting_ranges
    
    def _extract_format_type(self, text: str, pattern: str, format_type: str,
                           current_offset: int) -> Tuple[str, List[FormattingRange], int]:
        """Extract specific formatting type from text"""
        ranges = []
        modified_text = text
        total_offset = current_offset

        # Find all matches in original text
        matches = list(re.finditer(pattern, text))

        # Process matches in reverse order to maintain correct positions
        for match in reversed(matches):
            content = match.group(1)
            match_start = match.start()
            match_end = match.end()

            # Calculate position in the clean text (after previous replacements)
            clean_start = match_start - (total_offset - current_offset)
            clean_end = clean_start + len(content)

            # Create formatting range for the clean text
            formatting_range = FormattingRange(
                start=clean_start,
                end=clean_end,
                format_type=format_type
            )
            ranges.append(formatting_range)

            # Replace the markdown syntax with just the content
            modified_text = modified_text[:match_start] + content + modified_text[match_end:]

            # Update total offset
            syntax_length = match_end - match_start - len(content)
            total_offset += syntax_length

        # Reverse ranges to maintain original order
        ranges.reverse()

        return modified_text, ranges, total_offset
    
    def apply_formatting_to_requests(self, text: str, start_index: int, 
                                   formatting_ranges: List[FormattingRange]) -> List[Dict]:
        """
        Apply formatting ranges to Google Docs API requests
        
        Args:
            text: Clean text content
            start_index: Starting index in the document
            formatting_ranges: List of formatting ranges
            
        Returns:
            List of Google Docs API formatting requests
        """
        requests = []
        
        for fmt_range in formatting_ranges:
            try:
                request = self._create_formatting_request(
                    fmt_range, start_index, text
                )
                if request:
                    requests.append(request)
            except Exception as e:
                logger.warning(f"Failed to create formatting request for {fmt_range.format_type}: {e}")
                # Continue with other formatting
                continue
        
        return requests
    
    def _create_formatting_request(self, fmt_range: FormattingRange, 
                                 start_index: int, text: str) -> Optional[Dict]:
        """Create a single formatting request"""
        # Calculate actual positions using grapheme length
        text_before = text[:fmt_range.start]
        text_segment = text[fmt_range.start:fmt_range.end]
        
        actual_start = start_index + grapheme_len(text_before)
        actual_end = actual_start + grapheme_len(text_segment)
        
        # Validate range
        if actual_start >= actual_end:
            logger.warning(f"Invalid formatting range: {actual_start} >= {actual_end}")
            return None
        
        # Create request based on format type
        if fmt_range.format_type == 'bold':
            return {
                'updateTextStyle': {
                    'range': {
                        'startIndex': actual_start,
                        'endIndex': actual_end
                    },
                    'textStyle': {
                        'bold': True
                    },
                    'fields': 'bold'
                }
            }
        
        elif fmt_range.format_type == 'italic':
            return {
                'updateTextStyle': {
                    'range': {
                        'startIndex': actual_start,
                        'endIndex': actual_end
                    },
                    'textStyle': {
                        'italic': True
                    },
                    'fields': 'italic'
                }
            }
        
        elif fmt_range.format_type == 'code':
            return {
                'updateTextStyle': {
                    'range': {
                        'startIndex': actual_start,
                        'endIndex': actual_end
                    },
                    'textStyle': {
                        'weightedFontFamily': {
                            'fontFamily': 'Courier New'
                        },
                        'backgroundColor': {
                            'color': {
                                'rgbColor': {
                                    'red': 0.95,
                                    'green': 0.95,
                                    'blue': 0.95
                                }
                            }
                        }
                    },
                    'fields': 'weightedFontFamily,backgroundColor'
                }
            }
        
        elif fmt_range.format_type == 'strikethrough':
            return {
                'updateTextStyle': {
                    'range': {
                        'startIndex': actual_start,
                        'endIndex': actual_end
                    },
                    'textStyle': {
                        'strikethrough': True
                    },
                    'fields': 'strikethrough'
                }
            }
        
        return None
    
    def process_paragraph_with_formatting(self, text: str, start_index: int) -> Tuple[List[Dict], int]:
        """
        Process a paragraph with all formatting applied
        
        Args:
            text: Paragraph text with markdown formatting
            start_index: Starting index in the document
            
        Returns:
            Tuple of (requests, text_length)
        """
        try:
            # Extract formatting and get clean text
            clean_text, formatting_ranges = self.extract_formatting(text)
            
            # Add newline for paragraph
            text_with_newline = clean_text + '\n'
            text_length = grapheme_len(text_with_newline)
            
            requests = []
            
            # Insert the text
            requests.append({
                'insertText': {
                    'location': {'index': start_index},
                    'text': text_with_newline
                }
            })
            
            # Apply formatting
            formatting_requests = self.apply_formatting_to_requests(
                clean_text, start_index, formatting_ranges
            )
            requests.extend(formatting_requests)
            
            return requests, text_length
            
        except Exception as e:
            logger.error(f"Error processing paragraph formatting: {e}")
            # Fallback: insert text without formatting
            return self._fallback_plain_text(text, start_index)
    
    def _fallback_plain_text(self, text: str, start_index: int) -> Tuple[List[Dict], int]:
        """Fallback method to insert plain text when formatting fails"""
        logger.info("Using fallback plain text insertion")
        
        # Remove all markdown syntax for plain text
        clean_text = self._strip_all_markdown(text)
        text_with_newline = clean_text + '\n'
        text_length = grapheme_len(text_with_newline)
        
        request = {
            'insertText': {
                'location': {'index': start_index},
                'text': text_with_newline
            }
        }
        
        return [request], text_length
    
    def _strip_all_markdown(self, text: str) -> str:
        """Remove all markdown formatting syntax from text"""
        # Remove bold
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)
        text = re.sub(r'__([^_]+)__', r'\1', text)
        
        # Remove italic
        text = re.sub(r'\*([^*]+)\*', r'\1', text)
        text = re.sub(r'_([^_]+)_', r'\1', text)
        
        # Remove code
        text = re.sub(r'`([^`]+)`', r'\1', text)
        
        # Remove strikethrough
        text = re.sub(r'~~([^~]+)~~', r'\1', text)
        
        return text
    
    def validate_formatting_request(self, request: Dict) -> bool:
        """Validate a formatting request before sending to API"""
        try:
            if 'updateTextStyle' not in request:
                return True  # Not a formatting request
            
            update_style = request['updateTextStyle']
            
            # Check required fields
            if 'range' not in update_style:
                return False
            
            range_info = update_style['range']
            if 'startIndex' not in range_info or 'endIndex' not in range_info:
                return False
            
            # Validate range
            start = range_info['startIndex']
            end = range_info['endIndex']
            
            if not isinstance(start, int) or not isinstance(end, int):
                return False
            
            if start < 0 or end < 0 or start >= end:
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Error validating formatting request: {e}")
            return False
