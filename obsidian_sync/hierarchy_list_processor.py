"""
Enhanced hierarchy list processor with best practices
"""

from typing import List, Dict, <PERSON><PERSON>
from dataclasses import dataclass
from enum import Enum

class ListType(Enum):
    UNORDERED = "ul"
    ORDERED = "ol"

@dataclass
class ListItem:
    """Represents a list item with hierarchy information"""
    content: str
    list_type: ListType
    indent_level: int
    nesting_level: int
    line_number: int
    
    def is_ordered(self) -> bool:
        return self.list_type == ListType.ORDERED

class HierarchyListProcessor:
    """Enhanced processor for hierarchy lists with best practices"""
    
    # Constants for better maintainability
    INDENT_PER_LEVEL_PT = 36  # Points per nesting level
    TAB_SIZE = 4  # Treat tab as 4 spaces
    
    # Google Docs bullet presets for different levels
    UNORDERED_PRESETS = [
        'BULLET_DISC_CIRCLE_SQUARE',
        'BULLET_DIAMONDX_ARROW3D_SQUARE',
        'BULLET_CHECKBOX'
    ]
    
    ORDERED_PRESETS = [
        'NUMBERED_DECIMAL_ALPHA_ROMAN',
        'NUMBERED_DECIMAL_ALPHA_ROMAN_PARENS',
        'NUMBERED_DECIMAL_NESTED'
    ]
    
    def __init__(self, markdown_processor):
        """Initialize with reference to main markdown processor"""
        self.markdown_processor = markdown_processor
    
    def process_hierarchy_lists(self, lines: List[str], start_index: int) -> Tuple[List[Dict], int]:
        """
        Process hierarchy lists with enhanced structure and best practices
        
        Args:
            lines: List of markdown lines to process
            start_index: Starting index for Google Docs requests
            
        Returns:
            Tuple of (requests, total_length)
        """
        requests = []
        current_index = start_index
        
        # Parse list items with enhanced hierarchy detection
        list_items = self._parse_list_hierarchy(lines)
        
        # Generate Google Docs requests for each item
        for item in list_items:
            if item.content.strip():
                item_requests, item_length = self._create_list_item_requests(
                    item, current_index
                )
                requests.extend(item_requests)
                current_index += item_length
        
        return requests, current_index - start_index
    
    def _parse_list_hierarchy(self, lines: List[str]) -> List[ListItem]:
        """Parse lines into structured list items with proper hierarchy"""
        items = []
        nesting_stack = []  # Track hierarchy levels
        
        for line_num, line in enumerate(lines):
            if not self._is_list_item(line):
                continue
                
            # Calculate indentation and determine list type
            indent_level = self._calculate_indent_level(line)
            list_type, content = self._extract_list_content(line)
            
            # Calculate proper nesting level
            nesting_level = self._calculate_nesting_level(indent_level, nesting_stack)
            
            # Update nesting stack
            self._update_nesting_stack(nesting_stack, indent_level, list_type, nesting_level)
            
            # Create list item
            item = ListItem(
                content=content,
                list_type=list_type,
                indent_level=indent_level,
                nesting_level=nesting_level,
                line_number=line_num
            )
            items.append(item)
        
        return items
    
    def _is_list_item(self, line: str) -> bool:
        """Check if line is a list item with comprehensive patterns"""
        stripped = line.lstrip()
        
        # Unordered list patterns
        if stripped.startswith(('- ', '* ', '+ ')):
            return True
            
        # Ordered list patterns (more robust)
        if len(stripped) > 0 and stripped[0].isdigit():
            # Look for number followed by . or )
            for i, char in enumerate(stripped[1:], 1):
                if char in '. )':
                    return stripped[i+1:i+2] == ' '  # Must have space after
                elif not char.isdigit():
                    break
        
        return False
    
    def _calculate_indent_level(self, line: str) -> int:
        """Calculate indentation level with tab support"""
        indent = 0
        for char in line:
            if char == ' ':
                indent += 1
            elif char == '\t':
                indent += self.TAB_SIZE
            else:
                break
        return indent
    
    def _extract_list_content(self, line: str) -> Tuple[ListType, str]:
        """Extract list type and content from line"""
        stripped = line.lstrip()
        
        if stripped.startswith(('- ', '* ', '+ ')):
            return ListType.UNORDERED, stripped[2:].strip()
        else:
            # Ordered list - find the delimiter
            for i, char in enumerate(stripped):
                if char in '. )' and i > 0:
                    return ListType.ORDERED, stripped[i+1:].strip()
            
            # Fallback
            return ListType.UNORDERED, stripped
    
    def _calculate_nesting_level(self, current_indent: int, nesting_stack: List[Dict]) -> int:
        """Calculate proper nesting level based on indentation hierarchy"""
        if not nesting_stack:
            return 0

        # Remove items with greater or equal indentation
        while nesting_stack and nesting_stack[-1]['indent'] >= current_indent:
            nesting_stack.pop()

        # If stack is empty, this is a root level item
        if not nesting_stack:
            return 0

        # Return one level deeper than the last item in stack
        return nesting_stack[-1]['nesting_level'] + 1
    
    def _update_nesting_stack(self, stack: List[Dict], indent: int, list_type: ListType, nesting_level: int):
        """Update the nesting stack for hierarchy tracking"""
        # Remove entries with equal or greater indentation
        while stack and stack[-1]['indent'] >= indent:
            stack.pop()
        
        # Add current level
        stack.append({
            'indent': indent,
            'type': list_type,
            'nesting_level': nesting_level
        })
    
    def _create_list_item_requests(self, item: ListItem, start_index: int) -> Tuple[List[Dict], int]:
        """Create Google Docs requests for a single list item"""
        requests = []
        
        # Process math expressions in the text
        from .utils import grapheme_len
        processed_text, _ = self.markdown_processor._process_math_in_text(item.content, [])
        text_with_newline = processed_text + '\n'
        text_length = grapheme_len(text_with_newline)
        
        # Insert text request
        requests.append({
            'insertText': {
                'location': {'index': start_index},
                'text': text_with_newline
            }
        })
        
        # Apply list formatting
        bullet_preset = self._get_bullet_preset(item.is_ordered(), item.nesting_level)
        requests.append({
            'createParagraphBullets': {
                'range': {
                    'startIndex': start_index,
                    'endIndex': start_index + text_length - 1
                },
                'bulletPreset': bullet_preset
            }
        })
        
        # Apply indentation for nested items
        if item.nesting_level > 0:
            requests.append(self._create_indentation_request(
                start_index, text_length, item.nesting_level
            ))
        
        return requests, text_length
    
    def _get_bullet_preset(self, is_ordered: bool, nesting_level: int) -> str:
        """Get appropriate bullet preset for the list type and nesting level"""
        presets = self.ORDERED_PRESETS if is_ordered else self.UNORDERED_PRESETS
        return presets[min(nesting_level, len(presets) - 1)]
    
    def _create_indentation_request(self, start_index: int, text_length: int, nesting_level: int) -> Dict:
        """Create indentation request for nested list items"""
        indent_magnitude = nesting_level * self.INDENT_PER_LEVEL_PT
        
        return {
            'updateParagraphStyle': {
                'range': {
                    'startIndex': start_index,
                    'endIndex': start_index + text_length - 1
                },
                'paragraphStyle': {
                    'indentFirstLine': {
                        'magnitude': indent_magnitude,
                        'unit': 'PT'
                    },
                    'indentStart': {
                        'magnitude': indent_magnitude,
                        'unit': 'PT'
                    },
                    'direction': 'LEFT_TO_RIGHT'
                },
                'fields': 'indentFirstLine,indentStart,direction'
            }
        }
