"""
Codeblock Processor
Specialized processor for code blocks with syntax highlighting and fallback mechanisms
"""

import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from .utils import logger, grapheme_len


@dataclass
class CodeBlock:
    """Represents a code block in markdown"""
    content: str
    language: str
    start_line: int
    end_line: int
    indented: bool = False


class CodeblockProcessor:
    """Processor for code blocks with syntax highlighting support"""
    
    def __init__(self):
        """Initialize codeblock processor"""
        self.language_mappings = {
            'js': 'javascript',
            'ts': 'typescript',
            'py': 'python',
            'rb': 'ruby',
            'sh': 'bash',
            'yml': 'yaml',
            'md': 'markdown'
        }
        
        # Common programming languages for syntax detection
        self.language_keywords = {
            'python': ['def ', 'import ', 'from ', 'class ', 'if __name__'],
            'javascript': ['function ', 'const ', 'let ', 'var ', '=>', 'console.log'],
            'typescript': ['interface ', 'type ', 'function ', 'const ', '=>'],
            'java': ['public class', 'private ', 'public ', 'import java'],
            'cpp': ['#include', 'using namespace', 'int main', 'std::'],
            'csharp': ['using System', 'public class', 'namespace ', 'Console.WriteLine'],
            'go': ['package ', 'func ', 'import ', 'var ', 'fmt.Print'],
            'rust': ['fn ', 'let ', 'use ', 'struct ', 'impl '],
            'php': ['<?php', 'function ', '$', 'echo ', 'class '],
            'ruby': ['def ', 'class ', 'require ', 'puts ', 'end'],
            'bash': ['#!/bin/bash', 'echo ', 'if [', 'for ', 'while '],
            'sql': ['SELECT ', 'FROM ', 'WHERE ', 'INSERT ', 'UPDATE ']
        }
    
    def extract_codeblocks(self, content: str) -> Tuple[str, List[CodeBlock]]:
        """
        Extract code blocks from markdown content
        
        Args:
            content: Markdown content
            
        Returns:
            Tuple of (content_without_codeblocks, list_of_codeblocks)
        """
        codeblocks = []
        modified_content = content
        
        # Extract fenced code blocks (```language\ncode\n```)
        fenced_pattern = r'```(\w*)\n(.*?)\n```'
        fenced_blocks = self._extract_fenced_codeblocks(content, fenced_pattern)
        codeblocks.extend(fenced_blocks)
        
        # Remove fenced code blocks from content and replace with placeholders
        for i, block in enumerate(fenced_blocks):
            placeholder = f"[CODEBLOCK_{i}]"
            # Find and replace the original code block
            block_pattern = f"```{re.escape(block.language)}\n{re.escape(block.content)}\n```"
            modified_content = re.sub(block_pattern, placeholder, modified_content, count=1)
        
        # Extract indented code blocks (4+ spaces)
        indented_blocks = self._extract_indented_codeblocks(modified_content)
        codeblocks.extend(indented_blocks)
        
        # Remove indented code blocks from content and replace with placeholders
        for i, block in enumerate(indented_blocks):
            placeholder = f"[CODEBLOCK_{len(fenced_blocks) + i}]"
            # Replace indented code block with placeholder
            lines = modified_content.split('\n')
            for line_idx in range(block.start_line, block.end_line + 1):
                if line_idx < len(lines):
                    lines[line_idx] = placeholder if line_idx == block.start_line else ""
            modified_content = '\n'.join(lines)
        
        return modified_content, codeblocks
    
    def _extract_fenced_codeblocks(self, content: str, pattern: str) -> List[CodeBlock]:
        """Extract fenced code blocks (```language\ncode\n```)"""
        blocks = []
        
        for match in re.finditer(pattern, content, re.DOTALL):
            language = match.group(1).strip() or 'text'
            code_content = match.group(2)
            
            # Normalize language name
            language = self.language_mappings.get(language.lower(), language.lower())
            
            # Auto-detect language if not specified
            if language == 'text' or language == '':
                detected_language = self.detect_language(code_content)
                if detected_language:
                    language = detected_language
            
            # Find line numbers
            content_before = content[:match.start()]
            start_line = content_before.count('\n')
            end_line = start_line + code_content.count('\n') + 2  # +2 for ``` lines
            
            block = CodeBlock(
                content=code_content,
                language=language,
                start_line=start_line,
                end_line=end_line,
                indented=False
            )
            blocks.append(block)
        
        return blocks
    
    def _extract_indented_codeblocks(self, content: str) -> List[CodeBlock]:
        """Extract indented code blocks (4+ spaces or 1+ tabs)"""
        blocks = []
        lines = content.split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # Check if line is indented code (4+ spaces or 1+ tabs)
            if self._is_indented_code_line(line):
                # Find the end of the code block
                start_line = i
                code_lines = []
                
                while i < len(lines) and (self._is_indented_code_line(lines[i]) or lines[i].strip() == ''):
                    if lines[i].strip():  # Non-empty line
                        # Remove indentation
                        code_lines.append(self._remove_indentation(lines[i]))
                    else:
                        code_lines.append('')  # Preserve empty lines
                    i += 1
                
                # Remove trailing empty lines
                while code_lines and code_lines[-1] == '':
                    code_lines.pop()
                
                if code_lines:  # Only create block if there's actual content
                    code_content = '\n'.join(code_lines)
                    language = self.detect_language(code_content) or 'text'
                    
                    block = CodeBlock(
                        content=code_content,
                        language=language,
                        start_line=start_line,
                        end_line=i - 1,
                        indented=True
                    )
                    blocks.append(block)
            else:
                i += 1
        
        return blocks
    
    def _is_indented_code_line(self, line: str) -> bool:
        """Check if line is indented code (4+ spaces or 1+ tabs)"""
        if not line.strip():
            return False

        # Don't treat list items as code blocks
        stripped = line.lstrip()
        if (stripped.startswith('- ') or stripped.startswith('* ') or
            stripped.startswith('+ ') or
            (len(stripped) > 0 and stripped[0].isdigit() and '. ' in stripped[:10])):
            return False

        return line.startswith('    ') or line.startswith('\t')
    
    def _remove_indentation(self, line: str) -> str:
        """Remove code block indentation from line"""
        if line.startswith('    '):
            return line[4:]
        elif line.startswith('\t'):
            return line[1:]
        return line
    
    def detect_language(self, code_content: str) -> Optional[str]:
        """Auto-detect programming language from code content"""
        code_lower = code_content.lower()
        
        # Score each language based on keyword matches
        language_scores = {}
        
        for language, keywords in self.language_keywords.items():
            score = 0
            for keyword in keywords:
                score += code_lower.count(keyword.lower())
            
            if score > 0:
                language_scores[language] = score
        
        # Return language with highest score
        if language_scores:
            return max(language_scores, key=language_scores.get)
        
        return None
    
    def process_codeblock(self, block: CodeBlock, start_index: int) -> Tuple[List[Dict], int]:
        """
        Process a code block and return Google Docs API requests
        
        Args:
            block: CodeBlock to process
            start_index: Starting index in the document
            
        Returns:
            Tuple of (requests, text_length)
        """
        try:
            # Prepare code content with language label
            if block.language and block.language != 'text':
                code_text = f"[{block.language.upper()}]\n{block.content}\n\n"
            else:
                code_text = f"{block.content}\n\n"
            
            text_length = grapheme_len(code_text)
            
            requests = []
            
            # Insert the code text
            requests.append({
                'insertText': {
                    'location': {'index': start_index},
                    'text': code_text
                }
            })
            
            # Apply code formatting
            formatting_requests = self._apply_code_formatting(
                start_index, text_length, block.language
            )
            requests.extend(formatting_requests)
            
            return requests, text_length
            
        except Exception as e:
            logger.error(f"Error processing code block: {e}")
            return self._fallback_plain_code(block, start_index)
    
    def _apply_code_formatting(self, start_index: int, text_length: int, 
                             language: str) -> List[Dict]:
        """Apply formatting to code block"""
        requests = []
        
        try:
            # Apply monospace font and background
            requests.append({
                'updateTextStyle': {
                    'range': {
                        'startIndex': start_index,
                        'endIndex': start_index + text_length - 1  # Exclude final newline
                    },
                    'textStyle': {
                        'weightedFontFamily': {
                            'fontFamily': 'Courier New'
                        },
                        'backgroundColor': {
                            'color': {
                                'rgbColor': {
                                    'red': 0.96,
                                    'green': 0.96,
                                    'blue': 0.96
                                }
                            }
                        },
                        'fontSize': {
                            'magnitude': 10,
                            'unit': 'PT'
                        }
                    },
                    'fields': 'weightedFontFamily,backgroundColor,fontSize'
                }
            })
            
            # Add border-like effect with paragraph formatting
            requests.append({
                'updateParagraphStyle': {
                    'range': {
                        'startIndex': start_index,
                        'endIndex': start_index + text_length - 1
                    },
                    'paragraphStyle': {
                        'indentFirstLine': {
                            'magnitude': 18,
                            'unit': 'PT'
                        },
                        'indentStart': {
                            'magnitude': 18,
                            'unit': 'PT'
                        }
                    },
                    'fields': 'indentFirstLine,indentStart'
                }
            })
            
        except Exception as e:
            logger.warning(f"Error applying code formatting: {e}")
        
        return requests
    
    def _fallback_plain_code(self, block: CodeBlock, start_index: int) -> Tuple[List[Dict], int]:
        """Fallback method for code block processing"""
        logger.info("Using fallback plain code insertion")
        
        # Simple text insertion without formatting
        code_text = f"[CODE]\n{block.content}\n\n"
        text_length = grapheme_len(code_text)
        
        request = {
            'insertText': {
                'location': {'index': start_index},
                'text': code_text
            }
        }
        
        return [request], text_length
    
    def restore_codeblocks_in_content(self, content: str, codeblocks: List[CodeBlock]) -> str:
        """Restore code blocks in content by replacing placeholders"""
        restored_content = content
        
        for i, block in enumerate(codeblocks):
            placeholder = f"[CODEBLOCK_{i}]"
            if block.indented:
                # Restore as indented code block
                indented_lines = ['    ' + line for line in block.content.split('\n')]
                code_replacement = '\n'.join(indented_lines)
            else:
                # Restore as fenced code block
                code_replacement = f"```{block.language}\n{block.content}\n```"
            
            restored_content = restored_content.replace(placeholder, code_replacement)
        
        return restored_content
