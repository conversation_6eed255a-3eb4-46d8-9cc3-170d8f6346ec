# Test Note for Enhanced Sync

This note demonstrates all the enhanced features that have been fixed.

## Nested Lists Testing

### Unordered Lists with Proper Nesting
- First level item
  - Second level item
  - Another second level
    - Third level item
    - Another third level
      - Fourth level (deep nesting)
- Back to first level
  - Second level again

### Ordered Lists with Proper Nesting
1. First ordered item
   1. Nested ordered item
   2. Another nested ordered
      1. Deep nested ordered
      2. Another deep nested
2. Second first-level item
   1. Nested under second
   2. More nested items

### Mixed Lists (Ordered and Unordered)
1. Ordered item
   - Unordered nested
   - Another unordered
     1. Ordered deep nested
     2. More ordered deep
2. Another ordered item
   - Mixed nesting works
     - Even deeper unordered
       1. And back to ordered

## Bold Text Testing

This paragraph has **bold text** and __also bold__ formatting.

Multiple **bold** words in **the same** paragraph should work.

**Bold at start** and ending with **bold**.

Mixed formatting: **bold** and *italic* and `code` all together.

## Code Block Testing

### Fenced Code Blocks

Here's a Python function:

```python
def calculate_fibonacci(n):
    """Calculate the nth Fibonacci number."""
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

# Test the function
for i in range(10):
    print(f"F({i}) = {calculate_fibonacci(i)}")
```

JavaScript example:

```javascript
function greetUser(name, age) {
    const message = `Hello, ${name}! You are ${age} years old.`;
    console.log(message);
    
    if (age >= 18) {
        console.log("You are an adult.");
    } else {
        console.log("You are a minor.");
    }
}

greetUser("Alice", 25);
```

### Indented Code Blocks

    function simpleGreeting(name) {
        return "Hello, " + name + "!";
    }
    
    const greeting = simpleGreeting("World");
    console.log(greeting);

### Code with Language Detection

```
# This should be detected as bash/shell
#!/bin/bash
echo "Hello, World!"
for i in {1..5}; do
    echo "Count: $i"
done
```

## Inline Code Testing

Here's some `inline code` in a sentence.

Multiple `code snippets` in the `same paragraph` should work.

Mixed with **bold `code`** and *italic `code`* formatting.

## Image Testing

### Embedded Images (Obsidian Style)
Here's an embedded image: ![[test-image.png]]

Multiple images:
![[image1.jpg]]
![[image2.png]]

### Standard Markdown Images
![Alt text for image](standard-image.jpg)
![Another image](another-pic.png)

## Internal Links Testing

This links to [[Another Note]] which might not exist yet.

This links to [[Existing Note|with custom alias]].

Multiple links: [[Note 1]], [[Note 2]], and [[Note 3|Custom Name]].

## Complex Mixed Content

Here's a complex example with everything:

1. **Bold ordered item** with `inline code`
   - Nested unordered with **bold**
   - Another nested with `code`
     1. Deep ordered with **bold `code`**
     2. Link to [[Deep Note]]

```python
# Code block in list context
def complex_function():
    """This is inside a list."""
    return "Hello from nested code!"
```

2. Second item with image: ![[complex-image.png]]
   - More nesting with [[Internal Link]]

## Edge Cases

### Empty Lines in Lists
- Item 1

- Item 2 (with empty line above)
  
  - Nested with empty line

### Code in Lists
1. First item
   ```python
   print("Code in list")
   ```
2. Second item with `inline code`

### Bold and Code Combinations
**This is bold with `code inside`** the bold text.

`This is code with **bold inside**` the code.

**Bold** and `code` and **more bold**.

## Summary

This note tests:
- ✅ Proper nested list hierarchy (ordered and unordered)
- ✅ Bold text formatting with multiple syntaxes
- ✅ Code blocks (fenced and indented) with language detection
- ✅ Inline code formatting
- ✅ Image placeholders (embedded and standard markdown)
- ✅ Internal links with automatic target creation
- ✅ Complex mixed content scenarios
- ✅ Edge cases and error handling

All these features should now work correctly with the enhanced sync system!
